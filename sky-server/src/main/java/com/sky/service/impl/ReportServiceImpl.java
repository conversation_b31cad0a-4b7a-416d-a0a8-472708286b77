package com.sky.service.impl;

import com.sky.entity.Orders;
import com.sky.mapper.OrderMapper;
import com.sky.service.ReportService;
import com.sky.vo.OrderReportVO;
import com.sky.vo.SalesTop10ReportVO;
import com.sky.vo.TurnoverReportVO;
import com.sky.vo.UserReportVO;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
public class ReportServiceImpl implements ReportService {
    @Autowired
    private OrderMapper orderMapper;
    @Override
    public TurnoverReportVO statistics(LocalDate begin, LocalDate end) {
        List<LocalDate> list=new ArrayList<>();
        list.add(begin);
        while (begin!=end){
            begin=begin.plusDays(1);
            list.add(begin);
        }
        List<Double> turnoverList=new ArrayList<>();
        for(LocalDate date:list){
            LocalDateTime beginTime=LocalDateTime.of(date, LocalTime.MIN);
            LocalDateTime endTime=LocalDateTime.of(date,LocalTime.MAX);
            Map map=new HashMap();
            map.put("begin",beginTime);
            map.put("end",endTime);
            map.put("status", Orders.COMPLETED);
            Double turnover=orderMapper.sumByMap(map);
            turnover=turnover==null?0:turnover;
            turnoverList.add(turnover);
        }
        return  TurnoverReportVO.builder().dateList(StringUtils.join(list,",")).turnoverList(StringUtils.join(turnoverList,",")).build();

    }

    @Override
    public UserReportVO userStatistics(LocalDate begin, LocalDate end) {
        List<LocalDate> list=new ArrayList<>();
        list.add(begin);
        while (begin!=end){
            begin=begin.plusDays(1);
            list.add(begin);
        }
        List<Integer> usersList=new ArrayList<>();
        List<Integer> newUsersList=new ArrayList<>();
        for(LocalDate date:list){
            LocalDateTime beginTime=LocalDateTime.of(date, LocalTime.MIN);
            LocalDateTime endTime=LocalDateTime.of(date,LocalTime.MAX);
            Map map=new HashMap();
            map.put("end",endTime);
            Integer users=orderMapper.countBymap(map);
            usersList.add(users);
            map.put("begin",beginTime);
            Integer newUsers=orderMapper.countBymap(map);
            newUsersList.add(newUsers);
        }
        return  UserReportVO.builder().dateList(StringUtils.join(list,","))
                .totalUserList(StringUtils.join(usersList,","))
                .newUserList(StringUtils.join(newUsersList,",")).build();
    }

    @Override
    public OrderReportVO orderStatistics(LocalDate begin, LocalDate end) {
        List<LocalDate> list=new ArrayList<>();
        list.add(begin);
        while (begin!=end){
            begin=begin.plusDays(1);
            list.add(begin);
        }
        List<Integer> orderCountList=new ArrayList<>();
        List<Integer> validOrderCountList=new ArrayList<>();
        for(LocalDate date:list){
            LocalDateTime endTime=LocalDateTime.of(date,LocalTime.MAX);
            LocalDateTime beginTime=LocalDateTime.of(date,LocalTime.MIN);
            Map map=new HashMap();
            map.put("end",endTime);
            map.put("begin",beginTime);
            Integer orderCount=orderMapper.countOrder(map);
            orderCountList.add(orderCount);
            map.put("status",Orders.COMPLETED);
            Integer validOrderCount=orderMapper.countOrder(map);
            validOrderCountList.add(validOrderCount);
        }
        Integer voliadOrderCount = validOrderCountList.stream().reduce(Integer::sum).get();
        Integer orderCount = orderCountList.stream().reduce(Integer::sum).get();
        Double orderCompletionRate=0.0;
        if(orderCount!=0){
            orderCompletionRate=voliadOrderCount.doubleValue()/orderCount;
        }
        return OrderReportVO.builder().dateList(StringUtils.join(list,","))
                .orderCountList(StringUtils.join(orderCountList,","))
                .validOrderCountList(StringUtils.join(validOrderCountList,","))
                .totalOrderCount(voliadOrderCount)
                .orderCompletionRate(orderCompletionRate)
                .build();
    }

    @Override
    public SalesTop10ReportVO top(LocalDate begin, LocalDate end) {
        orderMapper.top(begin,end);

    }
}
