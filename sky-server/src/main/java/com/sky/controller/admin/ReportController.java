package com.sky.controller.admin;

import com.sky.result.Result;
import com.sky.service.ReportService;
import com.sky.vo.OrderReportVO;
import com.sky.vo.SalesTop10ReportVO;
import com.sky.vo.TurnoverReportVO;
import com.sky.vo.UserReportVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.formula.functions.T;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDate;
@Slf4j
@RestController
@RequestMapping("/admin/report")
public class ReportController {
    @Autowired
    private ReportService reportService;
    @GetMapping("/turnoverStatistics")
    public Result<TurnoverReportVO> turnoverStatistic(@DateTimeFormat (pattern = "yyyy-MM-dd")
                                                      LocalDate begin,
                                                      @DateTimeFormat (pattern = "yyyy-MM-dd")
                                                      LocalDate end){
        TurnoverReportVO turnoverReportVO=reportService.statistics(begin,end);
        return Result.success(turnoverReportVO);
    }
    @GetMapping("/userStatistics")
    public Result<UserReportVO> userStatistic(@DateTimeFormat (pattern = "yyyy-MM-dd")
                                                      LocalDate begin,
                                              @DateTimeFormat (pattern = "yyyy-MM-dd")
                                                      LocalDate end){
        UserReportVO userReportVO=reportService.userStatistics(begin,end);
        return Result.success(userReportVO);
    }

    @GetMapping("/ordersStatistics")
    public Result<OrderReportVO> orderStatistic(@DateTimeFormat (pattern = "yyyy-MM-dd")
                                                      LocalDate begin,
                                                @DateTimeFormat (pattern = "yyyy-MM-dd")
                                                      LocalDate end){
        OrderReportVO orderReportVO=reportService.orderStatistics(begin,end);
        return Result.success(orderReportVO);
    }


    @GetMapping
    public Result<SalesTop10ReportVO> top10(@DateTimeFormat (pattern = "yyyy-MM-dd")
                                                LocalDate begin,
                                            @DateTimeFormat (pattern = "yyyy-MM-dd")
                                                LocalDate end) {
        SalesTop10ReportVO salesTop10ReportVO=reportService.top(begin,end);
        return Result.success(salesTop10ReportVO);
    }
}
