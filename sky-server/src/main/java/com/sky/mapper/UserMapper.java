package com.sky.mapper;

import com.sky.annotation.Autofill;
import com.sky.entity.User;
import com.sky.enumeration.OperationType;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

/**
 * 用户映射器接口
 * 定义用户相关的数据库操作方法
 * 主要用于微信用户的登录和注册功能
 */
@Mapper
public interface UserMapper {

    /**
     * 根据微信openid查询用户
     * 用于用户登录时检查用户是否已存在
     *
     * @param openid 微信用户的唯一标识
     * @return User 用户实体对象，如果不存在则返回null
     */
    @Select("select * from user where openid = #{openid}")
    User queryByID(String openid);

    /**
     * 插入新用户
     * 向数据库中添加新的用户记录
     * 使用@Autofill注解自动填充创建时间等公共字段
     *
     * @param user 用户实体对象，包含用户的基本信息
     */
    @Autofill(value = OperationType.INSERT)
    @Insert("insert into user(openid, name, phone, sex, id_number, avatar, create_time) values (#{openid}, #{name}, #{phone}, #{sex}, #{idNumber}, #{avatar}, #{createTime})")
    void insert(User user);

}
