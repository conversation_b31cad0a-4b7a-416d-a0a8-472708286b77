package com.sky.config;

import com.sky.interceptor.JwtTokenAdminInterceptor;
import com.sky.interceptor.JwtTokenUserInterceptor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.converter.HttpMessageConverter;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.ResourceHandlerRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurationSupport;
import springfox.documentation.builders.ApiInfoBuilder;
import springfox.documentation.builders.PathSelectors;
import springfox.documentation.builders.RequestHandlerSelectors;
import springfox.documentation.service.ApiInfo;
import springfox.documentation.spi.DocumentationType;
import springfox.documentation.spring.web.plugins.Docket;

import java.util.List;

/**
 * 配置类，注册web层相关组件
 */
@Configuration
@Slf4j
public class WebMvcConfiguration extends WebMvcConfigurationSupport {

    @Autowired
    private JwtTokenAdminInterceptor jwtTokenAdminInterceptor;
    @Autowired
    private JwtTokenUserInterceptor jwtTokenUserInterceptor;

    /**
     * 注册自定义拦截器
     * 配置JWT令牌拦截器，用于验证用户身份和权限
     * 管理端拦截器：拦截/admin/**路径，排除登录接口
     * 用户端拦截器：拦截/user/**路径，排除登录和店铺状态查询接口
     *
     * @param registry 拦截器注册器
     */
    protected void addInterceptors(InterceptorRegistry registry) {
        log.info("开始注册自定义拦截器...");
        registry.addInterceptor(jwtTokenAdminInterceptor)
                .addPathPatterns("/admin/**")
                .excludePathPatterns("/admin/employee/login");
        registry.addInterceptor(jwtTokenUserInterceptor)
                .addPathPatterns("/user/**")
                .excludePathPatterns("/user/user/login")
                .excludePathPatterns("/user/shop/status");
    }

    /**
     * 通过knife4j生成接口文档
     * 配置Swagger文档生成器，用于生成API接口文档
     * 扫描controller包下的所有接口，生成在线API文档
     *
     * @return Docket Swagger文档配置对象
     */
    @Bean
    public Docket docket() {
        ApiInfo apiInfo = new ApiInfoBuilder()
                .title("苍穹外卖项目接口文档")
                .version("2.0")
                .description("苍穹外卖项目接口文档")
                .build();
        Docket docket = new Docket(DocumentationType.SWAGGER_2)
                .apiInfo(apiInfo)
                .select()
                .apis(RequestHandlerSelectors.basePackage("com.sky.controller"))
                .paths(PathSelectors.any())
                .build();
        return docket;
    }

    /**
     * 设置静态资源映射
     * 配置静态资源访问路径，主要用于Swagger文档页面的资源访问
     * 映射knife4j文档页面和相关的webjars资源
     * 同时添加音频文件和图片文件的静态资源映射，用于管理端声音提示功能
     *
     * @param registry 资源处理器注册器
     */
    protected void addResourceHandlers(ResourceHandlerRegistry registry) {
        log.info("开始设置静态资源映射...");
        registry.addResourceHandler("/doc.html").addResourceLocations("classpath:/META-INF/resources/");
        registry.addResourceHandler("/webjars/**").addResourceLocations("classpath:/META-INF/resources/webjars/");

        // 添加音频文件的静态资源映射，用于管理端订单提示音
        registry.addResourceHandler("/sounds/**").addResourceLocations("classpath:/static/sounds/");
        // 添加图片文件的静态资源映射，用于通知图标
        registry.addResourceHandler("/images/**").addResourceLocations("classpath:/static/images/");
    }

    /**
     * 扩展消息转换器
     * 用于配置自定义的HTTP消息转换器，如JSON序列化配置等
     * 目前已注释，使用Spring Boot默认的消息转换器配置
     *
     * @param converters 消息转换器列表
     */
//    protected  void extendMessageConverters(List<HttpMessageConverter<?>> converters){
//        super.extendMessageConverters(converters);
//    }


}

